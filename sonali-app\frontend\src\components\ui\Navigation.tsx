import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

export interface NavigationItem {
  key: string;
  label: string;
  path?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  badge?: string | number;
  external?: boolean;
}

export interface NavigationProps {
  items: NavigationItem[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'default' | 'pills' | 'underline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  mobileBreakpoint?: 'sm' | 'md' | 'lg' | 'xl';
}

export const Navigation: React.FC<NavigationProps> = ({
  items,
  orientation = 'horizontal',
  variant = 'default',
  size = 'md',
  className,
  mobileBreakpoint = 'md',
}) => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const isActive = (item: NavigationItem): boolean => {
    if (item.path) {
      return location.pathname === item.path;
    }
    return false;
  };

  const breakpointClasses = {
    sm: 'sm:hidden',
    md: 'md:hidden',
    lg: 'lg:hidden',
    xl: 'xl:hidden',
  };

  const showBreakpointClasses = {
    sm: 'sm:flex',
    md: 'md:flex',
    lg: 'lg:flex',
    xl: 'xl:flex',
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const getItemClasses = (item: NavigationItem, active: boolean) => {
    const baseClasses = cn(
      'inline-flex items-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
      sizeClasses[size],
      item.disabled && 'opacity-50 cursor-not-allowed'
    );

    switch (variant) {
      case 'pills':
        return cn(
          baseClasses,
          'rounded-lg',
          active
            ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
            : 'text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-800'
        );
      case 'underline':
        return cn(
          baseClasses,
          'border-b-2 rounded-none',
          active
            ? 'border-primary-500 text-primary-600 dark:text-primary-400'
            : 'border-transparent text-secondary-700 hover:text-secondary-900 hover:border-secondary-300 dark:text-secondary-300 dark:hover:text-secondary-100'
        );
      default:
        return cn(
          baseClasses,
          'rounded-md',
          active
            ? 'text-primary-600 dark:text-primary-400'
            : 'text-secondary-700 hover:text-secondary-900 dark:text-secondary-300 dark:hover:text-secondary-100'
        );
    }
  };

  const renderNavigationItem = (item: NavigationItem) => {
    const active = isActive(item);
    const itemClasses = getItemClasses(item, active);

    const content = (
      <>
        {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
        <span>{item.label}</span>
        {item.badge && (
          <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary-600 rounded-full">
            {item.badge}
          </span>
        )}
        {item.external && (
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        )}
      </>
    );

    const handleClick = () => {
      if (item.disabled) return;
      if (item.onClick) {
        item.onClick();
      }
      setMobileMenuOpen(false);
    };

    if (item.path && !item.onClick) {
      return (
        <Link
          key={item.key}
          to={item.path}
          className={itemClasses}
          onClick={() => setMobileMenuOpen(false)}
          target={item.external ? '_blank' : undefined}
          rel={item.external ? 'noopener noreferrer' : undefined}
        >
          {content}
        </Link>
      );
    }

    return (
      <button
        key={item.key}
        type="button"
        onClick={handleClick}
        className={itemClasses}
        disabled={item.disabled}
      >
        {content}
      </button>
    );
  };

  return (
    <nav className={cn('relative', className)}>
      {/* Mobile menu button */}
      <div className={cn('flex justify-end', breakpointClasses[mobileBreakpoint])}>
        <button
          type="button"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="inline-flex items-center justify-center p-2 rounded-md text-secondary-700 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:text-secondary-300 dark:hover:text-secondary-100 dark:hover:bg-secondary-800"
          aria-expanded={mobileMenuOpen}
          aria-label="Toggle navigation menu"
        >
          {mobileMenuOpen ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          )}
        </button>
      </div>

      {/* Desktop navigation */}
      <div className={cn('hidden', showBreakpointClasses[mobileBreakpoint])}>
        <div
          className={cn(
            'flex',
            orientation === 'vertical' ? 'flex-col space-y-1' : 'flex-row space-x-1'
          )}
        >
          {items.map(renderNavigationItem)}
        </div>
      </div>

      {/* Mobile navigation */}
      {mobileMenuOpen && (
        <div className={cn('absolute top-full left-0 right-0 z-50 mt-2', breakpointClasses[mobileBreakpoint])}>
          <div className="bg-white dark:bg-secondary-800 rounded-lg shadow-large border border-secondary-200 dark:border-secondary-700 py-2">
            <div className="flex flex-col space-y-1 px-2">
              {items.map(renderNavigationItem)}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};
