import { Router } from 'express';
import { financialController } from '@/controllers/financialController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { UserRole, TransactionType } from '@prisma/client';

const router = Router();

// Validation middleware
const validateTransactionId = [
  param('id')
    .isUUID()
    .withMessage('Invalid transaction ID format'),
];

const validateBranchId = [
  param('branchId')
    .isUUID()
    .withMessage('Invalid branch ID format'),
];

const validateCreateTransaction = [
  body('type')
    .isIn(Object.values(TransactionType))
    .withMessage('Invalid transaction type'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than zero'),
  body('description')
    .trim()
    .isLength({ min: 5, max: 500 })
    .withMessage('Description must be between 5 and 500 characters'),
  body('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  body('enteredBy')
    .optional()
    .isUUID()
    .withMessage('Invalid entered by user ID format'),
  body('referenceId')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Reference ID must be between 1 and 100 characters'),
  body('referenceType')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Reference type must be between 1 and 50 characters'),
];

const validateUpdateTransaction = [
  body('type')
    .optional()
    .isIn(Object.values(TransactionType))
    .withMessage('Invalid transaction type'),
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than zero'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 5, max: 500 })
    .withMessage('Description must be between 5 and 500 characters'),
  body('referenceId')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Reference ID must be between 1 and 100 characters'),
  body('referenceType')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Reference type must be between 1 and 50 characters'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('type')
    .optional()
    .isIn(Object.values(TransactionType))
    .withMessage('Invalid transaction type'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date format'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date format'),
  query('sortBy')
    .optional()
    .isIn(['transactionDate', 'amount', 'type', 'description'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

const validateSummaryQuery = [
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date format'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date format'),
];

const validateGenerateReport = [
  body('reportType')
    .isIn(['monthly_summary', 'branch_performance', 'loan_portfolio', 'collection_report'])
    .withMessage('Invalid report type'),
  body('parameters')
    .optional()
    .isObject()
    .withMessage('Parameters must be an object'),
  body('parameters.branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID in parameters'),
  body('parameters.year')
    .optional()
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Year must be between 2020 and 2030'),
  body('parameters.month')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('Month must be between 1 and 12'),
  body('parameters.startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date in parameters'),
  body('parameters.endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date in parameters'),
];

// All routes require authentication
router.use(authenticate);

// Transaction Routes

// GET /api/transactions - Get branch transactions (role-based)
router.get(
  '/transactions',
  requireRole([UserRole.admin, UserRole.manager]),
  validateQueryParams,
  handleValidationErrors,
  financialController.getTransactions.bind(financialController)
);

// POST /api/transactions - Create new transaction (manager only)
router.post(
  '/transactions',
  requireRole([UserRole.admin, UserRole.manager]),
  validateCreateTransaction,
  handleValidationErrors,
  financialController.createTransaction.bind(financialController)
);

// GET /api/transactions/summary - Get financial summary
router.get(
  '/transactions/summary',
  requireRole([UserRole.admin, UserRole.manager]),
  validateSummaryQuery,
  handleValidationErrors,
  financialController.getFinancialSummary.bind(financialController)
);

// GET /api/transactions/:id - Get transaction details
router.get(
  '/transactions/:id',
  requireRole([UserRole.admin, UserRole.manager]),
  validateTransactionId,
  handleValidationErrors,
  financialController.getTransactionById.bind(financialController)
);

// PUT /api/transactions/:id - Update transaction
router.put(
  '/transactions/:id',
  requireRole([UserRole.admin, UserRole.manager]),
  validateTransactionId,
  validateUpdateTransaction,
  handleValidationErrors,
  financialController.updateTransaction.bind(financialController)
);

// DELETE /api/transactions/:id - Delete transaction
router.delete(
  '/transactions/:id',
  requireRole([UserRole.admin, UserRole.manager]),
  validateTransactionId,
  handleValidationErrors,
  financialController.deleteTransaction.bind(financialController)
);

// Analytics Routes

// GET /api/analytics/branch/:branchId - Get branch analytics (admin/manager)
router.get(
  '/analytics/branch/:branchId',
  requireRole([UserRole.admin, UserRole.manager]),
  validateBranchId,
  handleValidationErrors,
  financialController.getBranchAnalytics.bind(financialController)
);

// GET /api/analytics/system - Get system-wide analytics (admin only)
router.get(
  '/analytics/system',
  requireRole([UserRole.admin]),
  financialController.getSystemAnalytics.bind(financialController)
);

// Reports Routes

// POST /api/reports/generate - Generate custom reports
router.post(
  '/reports/generate',
  requireRole([UserRole.admin, UserRole.manager]),
  validateGenerateReport,
  handleValidationErrors,
  financialController.generateCustomReport.bind(financialController)
);

export default router;
