import { prisma } from '@/config/database';
import { BranchTransaction, TransactionType, UserRole } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface CreateTransactionData {
  type: TransactionType;
  amount: number;
  description: string;
  branchId: string;
  createdBy: string;
  enteredBy?: string;
  referenceId?: string;
  referenceType?: string;
}

export interface UpdateTransactionData {
  type?: TransactionType;
  amount?: number;
  description?: string;
  referenceId?: string;
  referenceType?: string;
}

export interface TransactionListQuery {
  page?: number;
  limit?: number;
  search?: string;
  type?: TransactionType;
  branchId?: string;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FinancialSummary {
  totalIncome: number;
  totalExpense: number;
  netIncome: number;
  loanDisbursements: number;
  installmentCollections: number;
  savingsDeposits: number;
  savingsWithdrawals: number;
  transactionCount: number;
}

export interface BranchAnalytics {
  totalMembers: number;
  activeLoans: number;
  totalLoanAmount: number;
  collectedAmount: number;
  pendingAmount: number;
  overdueAmount: number;
  savingsAccounts: number;
  totalSavingsAmount: number;
  monthlyIncome: number;
  monthlyExpense: number;
  collectionRate: number;
  growthRate: number;
}

export interface SystemAnalytics {
  totalBranches: number;
  totalMembers: number;
  totalUsers: number;
  totalLoans: number;
  totalLoanAmount: number;
  totalCollectedAmount: number;
  totalSavingsAmount: number;
  monthlyGrowth: {
    members: number;
    loans: number;
    collections: number;
  };
  branchPerformance: Array<{
    branchId: string;
    branchName: string;
    performance: BranchAnalytics;
  }>;
}

export class FinancialService {
  async getTransactions(query: TransactionListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    transactions: (BranchTransaction & { branch?: any; creator?: any; enterer?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        type,
        branchId,
        startDate,
        endDate,
        sortBy = 'transactionDate',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.branchId = userBranchId;
      } else if (branchId) {
        where.branchId = branchId;
      }

      // Add type filter
      if (type) {
        where.type = type;
      }

      // Add date range filter
      if (startDate || endDate) {
        where.transactionDate = {};
        if (startDate) {
          where.transactionDate.gte = startDate;
        }
        if (endDate) {
          where.transactionDate.lte = endDate;
        }
      }

      // Add search filter
      if (search) {
        where.OR = [
          { description: { contains: search, mode: 'insensitive' } },
          { referenceId: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [transactions, total] = await Promise.all([
        prisma.branchTransaction.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            branch: {
              select: {
                id: true,
                name: true,
              },
            },
            creator: {
              select: {
                id: true,
                name: true,
              },
            },
            enterer: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        }),
        prisma.branchTransaction.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get transactions error:', error);
      throw error;
    }
  }

  async createTransaction(data: CreateTransactionData): Promise<BranchTransaction> {
    try {
      // Validate branch exists
      const branch = await prisma.branch.findUnique({
        where: { id: data.branchId },
        select: { id: true, isActive: true },
      });

      if (!branch) {
        throw new Error('Branch not found');
      }

      if (!branch.isActive) {
        throw new Error('Branch is not active');
      }

      const transaction = await prisma.branchTransaction.create({
        data: {
          ...data,
          transactionDate: new Date(),
          enteredBy: data.enteredBy || data.createdBy,
        },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      logger.info('Transaction created', {
        transactionId: transaction.id,
        type: transaction.type,
        amount: transaction.amount,
        branchId: transaction.branchId,
        createdBy: transaction.createdBy,
      });

      return transaction;
    } catch (error) {
      logger.error('Create transaction error:', error);
      throw error;
    }
  }

  async getTransactionById(id: string, userRole: UserRole, userBranchId?: string): Promise<BranchTransaction | null> {
    try {
      const transaction = await prisma.branchTransaction.findUnique({
        where: { id },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
          enterer: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Role-based access control
      if (transaction && userRole !== UserRole.admin && transaction.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view transactions from your own branch');
      }

      return transaction;
    } catch (error) {
      logger.error('Get transaction by ID error:', error);
      throw error;
    }
  }

  async updateTransaction(id: string, data: UpdateTransactionData, userRole: UserRole, userBranchId?: string): Promise<BranchTransaction> {
    try {
      // Check if transaction exists and user has access
      const existingTransaction = await prisma.branchTransaction.findUnique({
        where: { id },
        select: { id: true, branchId: true },
      });

      if (!existingTransaction) {
        throw new Error('Transaction not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && existingTransaction.branchId !== userBranchId) {
        throw new Error('Access denied: You can only update transactions from your own branch');
      }

      const transaction = await prisma.branchTransaction.update({
        where: { id },
        data,
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      logger.info('Transaction updated', {
        transactionId: transaction.id,
        updatedFields: Object.keys(data),
      });

      return transaction;
    } catch (error) {
      logger.error('Update transaction error:', error);
      throw error;
    }
  }

  async deleteTransaction(id: string, userRole: UserRole, userBranchId?: string): Promise<void> {
    try {
      // Check if transaction exists and user has access
      const transaction = await prisma.branchTransaction.findUnique({
        where: { id },
        select: { id: true, branchId: true, type: true, amount: true },
      });

      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && transaction.branchId !== userBranchId) {
        throw new Error('Access denied: You can only delete transactions from your own branch');
      }

      await prisma.branchTransaction.delete({
        where: { id }
      });

      logger.info('Transaction deleted', {
        deletedTransactionId: id,
        type: transaction.type,
        amount: transaction.amount,
      });
    } catch (error) {
      logger.error('Delete transaction error:', error);
      throw error;
    }
  }

  async getFinancialSummary(branchId?: string, startDate?: Date, endDate?: Date): Promise<FinancialSummary> {
    try {
      const where: any = {};

      if (branchId) {
        where.branchId = branchId;
      }

      if (startDate || endDate) {
        where.transactionDate = {};
        if (startDate) {
          where.transactionDate.gte = startDate;
        }
        if (endDate) {
          where.transactionDate.lte = endDate;
        }
      }

      const [incomeStats, expenseStats, transactionCount] = await Promise.all([
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            type: TransactionType.income,
          },
          _sum: { amount: true },
        }),
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            type: TransactionType.expense,
          },
          _sum: { amount: true },
        }),
        prisma.branchTransaction.count({ where }),
      ]);

      const totalIncome = incomeStats._sum.amount || 0;
      const totalExpense = expenseStats._sum.amount || 0;
      const netIncome = totalIncome - totalExpense;

      // Get specific transaction types
      const [loanDisbursements, installmentCollections, savingsDeposits, savingsWithdrawals] = await Promise.all([
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            referenceType: 'loan_disbursement',
          },
          _sum: { amount: true },
        }),
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            referenceType: 'installment_collection',
          },
          _sum: { amount: true },
        }),
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            referenceType: 'savings_deposit',
          },
          _sum: { amount: true },
        }),
        prisma.branchTransaction.aggregate({
          where: {
            ...where,
            referenceType: 'savings_withdrawal',
          },
          _sum: { amount: true },
        }),
      ]);

      return {
        totalIncome,
        totalExpense,
        netIncome,
        loanDisbursements: loanDisbursements._sum.amount || 0,
        installmentCollections: installmentCollections._sum.amount || 0,
        savingsDeposits: savingsDeposits._sum.amount || 0,
        savingsWithdrawals: savingsWithdrawals._sum.amount || 0,
        transactionCount,
      };
    } catch (error) {
      logger.error('Get financial summary error:', error);
      throw error;
    }
  }

  async getBranchAnalytics(branchId: string): Promise<BranchAnalytics> {
    try {
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const previousMonth = new Date(currentMonth);
      previousMonth.setMonth(previousMonth.getMonth() - 1);

      const [
        totalMembers,
        activeLoans,
        loanStats,
        installmentStats,
        savingsStats,
        monthlyIncome,
        monthlyExpense,
        previousMonthStats
      ] = await Promise.all([
        // Total members
        prisma.member.count({
          where: { branchId, isActive: true },
        }),

        // Active loans count
        prisma.loan.count({
          where: {
            loanApplication: {
              member: { branchId },
            },
          },
        }),

        // Loan statistics
        prisma.loan.aggregate({
          where: {
            loanApplication: {
              member: { branchId },
            },
          },
          _sum: {
            loanAmount: true,
            totalRepaymentAmount: true,
          },
        }),

        // Installment statistics
        prisma.installmentCollection.aggregate({
          where: {
            installment: {
              loan: {
                loanApplication: {
                  member: { branchId },
                },
              },
            },
          },
          _sum: {
            collectedAmount: true,
          },
        }),

        // Savings statistics
        prisma.savingAccount.aggregate({
          where: {
            member: { branchId },
            isActive: true,
          },
          _count: true,
          _sum: {
            monthlyAmount: true,
            fdrAmount: true,
          },
        }),

        // Monthly income
        prisma.branchTransaction.aggregate({
          where: {
            branchId,
            type: TransactionType.income,
            transactionDate: {
              gte: currentMonth,
            },
          },
          _sum: { amount: true },
        }),

        // Monthly expense
        prisma.branchTransaction.aggregate({
          where: {
            branchId,
            type: TransactionType.expense,
            transactionDate: {
              gte: currentMonth,
            },
          },
          _sum: { amount: true },
        }),

        // Previous month stats for growth calculation
        prisma.member.count({
          where: {
            branchId,
            isActive: true,
            createdAt: {
              lt: currentMonth,
            },
          },
        }),
      ]);

      // Calculate collection rate and growth rate
      const totalLoanAmount = loanStats._sum.loanAmount || 0;
      const collectedAmount = installmentStats._sum.collectedAmount || 0;
      const collectionRate = totalLoanAmount > 0 ? (collectedAmount / totalLoanAmount) * 100 : 0;

      const growthRate = previousMonthStats > 0 ? 
        ((totalMembers - previousMonthStats) / previousMonthStats) * 100 : 0;

      return {
        totalMembers,
        activeLoans,
        totalLoanAmount,
        collectedAmount,
        pendingAmount: Math.max(0, totalLoanAmount - collectedAmount),
        overdueAmount: 0, // Would need more complex calculation
        savingsAccounts: savingsStats._count,
        totalSavingsAmount: (savingsStats._sum.monthlyAmount || 0) + (savingsStats._sum.fdrAmount || 0),
        monthlyIncome: monthlyIncome._sum.amount || 0,
        monthlyExpense: monthlyExpense._sum.amount || 0,
        collectionRate: Math.round(collectionRate * 100) / 100,
        growthRate: Math.round(growthRate * 100) / 100,
      };
    } catch (error) {
      logger.error('Get branch analytics error:', error);
      throw error;
    }
  }

  async getSystemAnalytics(): Promise<SystemAnalytics> {
    try {
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const previousMonth = new Date(currentMonth);
      previousMonth.setMonth(previousMonth.getMonth() - 1);

      const [
        totalBranches,
        totalMembers,
        totalUsers,
        totalLoans,
        systemStats,
        monthlyGrowthStats,
        branches
      ] = await Promise.all([
        prisma.branch.count({ where: { isActive: true } }),
        prisma.member.count({ where: { isActive: true } }),
        prisma.user.count({ where: { isActive: true } }),
        prisma.loan.count(),
        
        // System-wide financial stats
        Promise.all([
          prisma.loan.aggregate({ _sum: { loanAmount: true } }),
          prisma.installmentCollection.aggregate({ _sum: { collectedAmount: true } }),
          prisma.savingAccount.aggregate({ 
            _sum: { monthlyAmount: true, fdrAmount: true },
            where: { isActive: true }
          }),
        ]),

        // Monthly growth stats
        Promise.all([
          prisma.member.count({
            where: {
              isActive: true,
              createdAt: { gte: currentMonth },
            },
          }),
          prisma.loan.count({
            where: {
              loanDate: { gte: currentMonth },
            },
          }),
          prisma.installmentCollection.aggregate({
            where: {
              collectionDate: { gte: currentMonth },
            },
            _sum: { collectedAmount: true },
          }),
        ]),

        // Get all branches for performance calculation
        prisma.branch.findMany({
          where: { isActive: true },
          select: { id: true, name: true },
        }),
      ]);

      const [loanAmountStats, collectionStats, savingsStats] = systemStats;
      const [newMembers, newLoans, monthlyCollections] = monthlyGrowthStats;

      // Calculate branch performance
      const branchPerformance = await Promise.all(
        branches.map(async (branch) => ({
          branchId: branch.id,
          branchName: branch.name,
          performance: await this.getBranchAnalytics(branch.id),
        }))
      );

      return {
        totalBranches,
        totalMembers,
        totalUsers,
        totalLoans,
        totalLoanAmount: loanAmountStats._sum.loanAmount || 0,
        totalCollectedAmount: collectionStats._sum.collectedAmount || 0,
        totalSavingsAmount: (savingsStats._sum.monthlyAmount || 0) + (savingsStats._sum.fdrAmount || 0),
        monthlyGrowth: {
          members: newMembers,
          loans: newLoans,
          collections: monthlyCollections._sum.collectedAmount || 0,
        },
        branchPerformance,
      };
    } catch (error) {
      logger.error('Get system analytics error:', error);
      throw error;
    }
  }

  async generateCustomReport(reportType: string, parameters: any): Promise<any> {
    try {
      switch (reportType) {
        case 'monthly_summary':
          return await this.generateMonthlySummaryReport(parameters);
        case 'branch_performance':
          return await this.generateBranchPerformanceReport(parameters);
        case 'loan_portfolio':
          return await this.generateLoanPortfolioReport(parameters);
        case 'collection_report':
          return await this.generateCollectionReport(parameters);
        default:
          throw new Error('Invalid report type');
      }
    } catch (error) {
      logger.error('Generate custom report error:', error);
      throw error;
    }
  }

  private async generateMonthlySummaryReport(parameters: any): Promise<any> {
    const { branchId, year, month } = parameters;

    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59, 999);

    const summary = await this.getFinancialSummary(branchId, startDate, endDate);

    return {
      reportType: 'Monthly Summary',
      period: `${year}-${month.toString().padStart(2, '0')}`,
      branchId,
      data: summary,
      generatedAt: new Date(),
    };
  }

  private async generateBranchPerformanceReport(parameters: any): Promise<any> {
    const { branchIds, startDate, endDate } = parameters;

    const branches = branchIds || await prisma.branch.findMany({
      where: { isActive: true },
      select: { id: true, name: true },
    });

    const performance = await Promise.all(
      branches.map(async (branch: any) => ({
        branchId: branch.id,
        branchName: branch.name,
        analytics: await this.getBranchAnalytics(branch.id),
        financialSummary: await this.getFinancialSummary(
          branch.id,
          startDate ? new Date(startDate) : undefined,
          endDate ? new Date(endDate) : undefined
        ),
      }))
    );

    return {
      reportType: 'Branch Performance',
      period: `${startDate || 'All time'} to ${endDate || 'Present'}`,
      data: performance,
      generatedAt: new Date(),
    };
  }

  private async generateLoanPortfolioReport(parameters: any): Promise<any> {
    const { branchId } = parameters;

    const where: any = {};
    if (branchId) {
      where.loanApplication = { member: { branchId } };
    }

    const [totalLoans, loanStats, statusBreakdown] = await Promise.all([
      prisma.loan.count({ where }),
      prisma.loan.aggregate({
        where,
        _sum: { loanAmount: true, totalRepaymentAmount: true },
        _avg: { loanAmount: true },
      }),
      prisma.loanApplication.groupBy({
        by: ['status'],
        _count: { status: true },
        where: branchId ? { member: { branchId } } : {},
      }),
    ]);

    return {
      reportType: 'Loan Portfolio',
      branchId,
      data: {
        totalLoans,
        totalLoanAmount: loanStats._sum.loanAmount || 0,
        totalRepaymentAmount: loanStats._sum.totalRepaymentAmount || 0,
        averageLoanAmount: loanStats._avg.loanAmount || 0,
        statusBreakdown,
      },
      generatedAt: new Date(),
    };
  }

  private async generateCollectionReport(parameters: any): Promise<any> {
    const { branchId, startDate, endDate } = parameters;

    const where: any = {};
    if (branchId) {
      where.installment = {
        loan: {
          loanApplication: {
            member: { branchId }
          }
        }
      };
    }

    if (startDate || endDate) {
      where.collectionDate = {};
      if (startDate) where.collectionDate.gte = new Date(startDate);
      if (endDate) where.collectionDate.lte = new Date(endDate);
    }

    const [totalCollections, collectionStats, dailyCollections] = await Promise.all([
      prisma.installmentCollection.count({ where }),
      prisma.installmentCollection.aggregate({
        where,
        _sum: { collectedAmount: true },
        _avg: { collectedAmount: true },
      }),
      prisma.installmentCollection.groupBy({
        by: ['collectionDate'],
        where,
        _sum: { collectedAmount: true },
        _count: { id: true },
        orderBy: { collectionDate: 'asc' },
      }),
    ]);

    return {
      reportType: 'Collection Report',
      branchId,
      period: `${startDate || 'All time'} to ${endDate || 'Present'}`,
      data: {
        totalCollections,
        totalAmount: collectionStats._sum.collectedAmount || 0,
        averageAmount: collectionStats._avg.collectedAmount || 0,
        dailyBreakdown: dailyCollections,
      },
      generatedAt: new Date(),
    };
  }
}

export const financialService = new FinancialService();
