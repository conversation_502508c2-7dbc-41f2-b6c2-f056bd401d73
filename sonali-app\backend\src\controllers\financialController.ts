import { Request, Response } from 'express';
import { 
  financialService, 
  TransactionListQuery, 
  CreateTransactionData, 
  UpdateTransactionData 
} from '@/services/financialService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole, TransactionType } from '@prisma/client';

export class FinancialController {
  async getTransactions(req: Request, res: Response): Promise<void> {
    try {
      const query: TransactionListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        type: req.query.type as TransactionType,
        branchId: req.query.branchId as string,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        sortBy: req.query.sortBy as string || 'transactionDate',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await financialService.getTransactions(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Transactions retrieved successfully');
    } catch (error) {
      logger.error('Get transactions controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve transactions');
    }
  }

  async createTransaction(req: Request, res: Response): Promise<void> {
    try {
      const transactionData: CreateTransactionData = {
        type: req.body.type,
        amount: req.body.amount,
        description: req.body.description,
        branchId: req.body.branchId || req.user?.branchId!,
        createdBy: req.userId!,
        enteredBy: req.body.enteredBy,
        referenceId: req.body.referenceId,
        referenceType: req.body.referenceType,
      };

      const transaction = await financialService.createTransaction(transactionData);

      ResponseUtil.success(res, transaction, 'Transaction created successfully', 201);
    } catch (error) {
      logger.error('Create transaction controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found') || 
            error.message.includes('not active')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to create transaction');
    }
  }

  async getTransactionById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const transaction = await financialService.getTransactionById(id, userRole, userBranchId);

      if (!transaction) {
        ResponseUtil.notFound(res, 'Transaction not found');
        return;
      }

      ResponseUtil.success(res, transaction, 'Transaction retrieved successfully');
    } catch (error) {
      logger.error('Get transaction by ID controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve transaction');
    }
  }

  async updateTransaction(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const updateData: UpdateTransactionData = {
        type: req.body.type,
        amount: req.body.amount,
        description: req.body.description,
        referenceId: req.body.referenceId,
        referenceType: req.body.referenceType,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof UpdateTransactionData] === undefined) {
          delete updateData[key as keyof UpdateTransactionData];
        }
      });

      const transaction = await financialService.updateTransaction(id, updateData, userRole, userBranchId);

      ResponseUtil.success(res, transaction, 'Transaction updated successfully');
    } catch (error) {
      logger.error('Update transaction controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update transaction');
    }
  }

  async deleteTransaction(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      await financialService.deleteTransaction(id, userRole, userBranchId);

      ResponseUtil.success(res, null, 'Transaction deleted successfully');
    } catch (error) {
      logger.error('Delete transaction controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to delete transaction');
    }
  }

  async getFinancialSummary(req: Request, res: Response): Promise<void> {
    try {
      const branchId = req.query.branchId as string || req.user?.branchId;
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      // Role-based access control
      if (req.user?.role !== UserRole.admin && branchId !== req.user?.branchId) {
        ResponseUtil.forbidden(res, 'Access denied: You can only view summary for your own branch');
        return;
      }

      const summary = await financialService.getFinancialSummary(branchId, startDate, endDate);

      ResponseUtil.success(res, summary, 'Financial summary retrieved successfully');
    } catch (error) {
      logger.error('Get financial summary controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve financial summary');
    }
  }

  async getBranchAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { branchId } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      // Role-based access control
      if (userRole !== UserRole.admin && userRole !== UserRole.manager) {
        ResponseUtil.forbidden(res, 'Access denied: Only admins and managers can view analytics');
        return;
      }

      if (userRole === UserRole.manager && branchId !== userBranchId) {
        ResponseUtil.forbidden(res, 'Access denied: You can only view analytics for your own branch');
        return;
      }

      const analytics = await financialService.getBranchAnalytics(branchId);

      ResponseUtil.success(res, analytics, 'Branch analytics retrieved successfully');
    } catch (error) {
      logger.error('Get branch analytics controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve branch analytics');
    }
  }

  async getSystemAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const userRole = req.user?.role as UserRole;

      // Only admins can view system-wide analytics
      if (userRole !== UserRole.admin) {
        ResponseUtil.forbidden(res, 'Access denied: Only admins can view system analytics');
        return;
      }

      const analytics = await financialService.getSystemAnalytics();

      ResponseUtil.success(res, analytics, 'System analytics retrieved successfully');
    } catch (error) {
      logger.error('Get system analytics controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve system analytics');
    }
  }

  async generateCustomReport(req: Request, res: Response): Promise<void> {
    try {
      const { reportType } = req.body;
      const parameters = req.body.parameters || {};

      // Role-based access control for report parameters
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      if (userRole !== UserRole.admin) {
        // Non-admin users can only generate reports for their own branch
        if (parameters.branchId && parameters.branchId !== userBranchId) {
          ResponseUtil.forbidden(res, 'Access denied: You can only generate reports for your own branch');
          return;
        }
        // Set branch ID to user's branch if not specified
        if (!parameters.branchId) {
          parameters.branchId = userBranchId;
        }
      }

      const report = await financialService.generateCustomReport(reportType, parameters);

      ResponseUtil.success(res, report, 'Custom report generated successfully');
    } catch (error) {
      logger.error('Generate custom report controller error:', error);
      
      if (error instanceof Error && error.message.includes('Invalid report type')) {
        ResponseUtil.error(res, error.message, 400);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to generate custom report');
    }
  }
}

export const financialController = new FinancialController();
