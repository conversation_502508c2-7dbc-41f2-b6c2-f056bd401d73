import React from 'react';
import { Header } from './Header';
import { authService } from '../../services/authService';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticated();
  const user = authService.getStoredUser();

  return (
    <div className="min-h-screen bg-secondary-50 dark:bg-secondary-900 transition-colors">
      <Header isAuthenticated={isAuthenticated} user={user} />
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
};
