# Sonali App API Documentation

## Complete API Endpoints Overview

The Sonali App backend provides 57 comprehensive API endpoints across 7 main categories:

1. **Authentication APIs** (8 endpoints) - JWT-based auth with refresh tokens
2. **User Management APIs** (9 endpoints) - Admin-only user CRUD operations
3. **Branch Management APIs** (8 endpoints) - Role-based branch management
4. **Member Management APIs** (8 endpoints) - Member CRUD with photo upload
5. **Loan Management APIs** (8 endpoints) - Loan application workflow
6. **Installment Collection APIs** (8 endpoints) - Payment tracking and collection
7. **Financial Management APIs** (8 endpoints) - Transactions and analytics

## Authentication Endpoints

### POST /api/auth/login
Login with Member ID or Email

**Request Body:**
```json
{
  "identifier": "MEM001", // Member ID or email
  "password": "password123",
  "rememberMe": false // Optional, default false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "name": "User Name",
      "email": "<EMAIL>",
      "memberId": "MEM001",
      "role": "member",
      "isActive": true
    },
    "accessToken": "jwt_access_token",
    "sessionId": "session_id",
    "expiresAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Login successful"
}
```

**Rate Limiting:** 5 attempts per 15 minutes per IP

### POST /api/auth/register
Register a new user

**Request Body:**
```json
{
  "name": "Full Name",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "member", // Optional, default "member"
  "memberId": "MEM001", // Optional
  "branchId": "branch_id" // Optional
}
```

### POST /api/auth/refresh
Refresh access token

**Request:** Uses HTTP-only cookie or body
```json
{
  "refreshToken": "refresh_token" // Optional if cookie is set
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_access_token"
  }
}
```

### POST /api/auth/logout
Logout current session

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

### POST /api/auth/logout-all
Logout from all devices

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Logged out from all devices successfully"
}
```

### POST /api/auth/forgot-password
Initiate password reset

**Request Body:**
```json
{
  "identifier": "<EMAIL>" // Email or Member ID
}
```

**Response:**
```json
{
  "success": true,
  "message": "If an account with that identifier exists, a password reset link has been sent."
}
```

**Rate Limiting:** 3 attempts per hour per IP

### POST /api/auth/reset-password
Reset password with token

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password has been reset successfully. Please log in with your new password."
}
```

### GET /api/auth/profile
Get user profile

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "memberId": "MEM001",
    "role": "member",
    "isActive": true,
    "lastLoginAt": "2024-01-01T00:00:00.000Z",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /api/auth/profile
Update user profile

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
  "name": "Updated Name",
  "email": "<EMAIL>"
}
```

### PUT /api/auth/change-password
Change password

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
  "currentPassword": "currentpass",
  "newPassword": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

### GET /api/auth/sessions
Get active sessions

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "session_id",
      "sessionId": "session_identifier",
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0...",
      "isActive": true,
      "rememberMe": false,
      "lastActivity": "2024-01-01T00:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### DELETE /api/auth/sessions/:sessionId
Revoke a specific session

**Headers:** `Authorization: Bearer <access_token>`

**Response:**
```json
{
  "success": true,
  "message": "Session revoked successfully"
}
```

## Error Responses

### Authentication Errors
```json
{
  "success": false,
  "error": "Invalid credentials"
}
```

### Rate Limiting Errors
```json
{
  "success": false,
  "error": "Too many login attempts. Please try again in 15 minutes."
}
```

### Validation Errors
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "password",
      "message": "Password must be at least 8 characters long"
    }
  ]
}
```

## Security Headers

All API responses include security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Content-Security-Policy: default-src 'self'`

## Rate Limiting

Different endpoints have different rate limits:
- **General API**: 100 requests per 15 minutes per IP
- **Authentication**: 5 attempts per 15 minutes per IP
- **Password Reset**: 3 attempts per hour per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time

## Authentication Flow

1. **Login**: POST to `/api/auth/login` with credentials
2. **Store Token**: Save access token and use in Authorization header
3. **API Calls**: Include `Authorization: Bearer <token>` header
4. **Token Refresh**: Use refresh token when access token expires
5. **Logout**: POST to `/api/auth/logout` to invalidate session

## User Management Endpoints (Admin Only)

### GET /api/users
Get all users with pagination and filtering

**Headers:** `Authorization: Bearer <access_token>`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search in name, email, memberId
- `role` (optional): Filter by user role
- `isActive` (optional): Filter by active status
- `branchId` (optional): Filter by branch
- `sortBy` (optional): Sort field (name, email, role, createdAt, lastLoginAt)
- `sortOrder` (optional): Sort order (asc, desc)

### POST /api/users
Create new user

**Headers:** `Authorization: Bearer <access_token>`

**Request Body:**
```json
{
  "name": "Full Name",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "role": "member",
  "memberId": "MEM001",
  "branchId": "branch_id"
}
```

### GET /api/users/stats
Get user statistics

### GET /api/users/export
Export users data to Excel

### POST /api/users/bulk-import
Bulk import users

**Request Body:**
```json
{
  "users": [
    {
      "name": "User 1",
      "email": "<EMAIL>",
      "password": "password123",
      "role": "member"
    }
  ]
}
```

### GET /api/users/:id
Get user by ID

### PUT /api/users/:id
Update user

### DELETE /api/users/:id
Delete user

### PUT /api/users/:id/reset-password
Reset user password

### PUT /api/users/:id/toggle-status
Activate/deactivate user

## Branch Management Endpoints

### GET /api/branches
Get branches (admin: all, manager/field officer: own branch)

### POST /api/branches
Create new branch (admin only)

### GET /api/branches/:id
Get branch details

### PUT /api/branches/:id
Update branch (admin only)

### DELETE /api/branches/:id
Delete branch (admin only)

### GET /api/branches/:id/users
Get branch users

### GET /api/branches/:id/members
Get branch members

### GET /api/branches/:id/performance
Get branch performance metrics

## Member Management Endpoints

### GET /api/members
Get members with role-based filtering

### POST /api/members
Create new member

**Request Body:**
```json
{
  "memberId": "MEM001",
  "name": "Member Name",
  "fatherOrHusbandName": "Father Name",
  "motherName": "Mother Name",
  "presentAddress": "Present Address",
  "permanentAddress": "Permanent Address",
  "nidNumber": "1234567890123",
  "dateOfBirth": "1990-01-01",
  "religion": "Islam",
  "phoneNumber": "***********",
  "bloodGroup": "A+",
  "occupation": "Business",
  "referenceId": "reference_member_id",
  "branchId": "branch_id"
}
```

### GET /api/members/:id
Get member details

### PUT /api/members/:id
Update member

### DELETE /api/members/:id
Delete member

### GET /api/members/search
Search members with autocomplete

**Query Parameters:**
- `q`: Search query (required)
- `branchId` (optional): Filter by branch
- `limit` (optional): Result limit (default: 10)

### POST /api/members/:id/upload-photo
Upload member photo

**Content-Type:** `multipart/form-data`
**File Field:** `photo`
**Max Size:** 5MB
**Allowed Types:** jpeg, jpg, png, gif

### GET /api/members/export
Export members data (admin only)

## Loan Management Endpoints

### GET /api/loan-applications
Get loan applications with role-based filtering

### POST /api/loan-applications
Create new loan application

**Request Body:**
```json
{
  "memberId": "member_id",
  "appliedAmount": 50000,
  "reason": "Business expansion",
  "loanCycleNumber": 1,
  "recommender": "Recommender Name",
  "advancePayment": 5000
}
```

### GET /api/loan-applications/:id
Get loan application details

### PUT /api/loan-applications/:id
Update loan application

### PUT /api/loan-applications/:id/approve
Approve loan application (manager only)

### PUT /api/loan-applications/:id/reject
Reject loan application (manager only)

**Request Body:**
```json
{
  "rejectionReason": "Insufficient documentation"
}
```

### GET /api/loans
Get active loans

### GET /api/loans/:id
Get loan details

### GET /api/loans/:id/installments
Get loan installment schedule

### POST /api/loans/calculator
Loan calculation endpoint

**Request Body:**
```json
{
  "loanAmount": 50000,
  "repaymentDuration": 12,
  "repaymentMethod": "monthly",
  "advancePayment": 5000,
  "interestRate": 10
}
```

## Installment Collection Endpoints

### GET /api/installments
Get installments with role-based filtering

### GET /api/installments/pending
Get pending installments

### GET /api/installments/overdue
Get overdue installments

### POST /api/installments/:id/collect
Collect installment payment

**Request Body:**
```json
{
  "collectedAmount": 5000,
  "collectionDate": "2024-01-01T10:00:00Z",
  "notes": "Payment collected in cash"
}
```

### GET /api/installments/:id/history
Get installment payment history

### GET /api/installments/member/:memberId
Get member installments

### PUT /api/installments/:id/status
Update installment status

### POST /api/installments/update-overdue
Update overdue installments (admin/manager only)

## Financial Management Endpoints

### GET /api/transactions
Get branch transactions (role-based)

### POST /api/transactions
Create new transaction (manager only)

**Request Body:**
```json
{
  "type": "income",
  "amount": 10000,
  "description": "Service fee collection",
  "branchId": "branch_id",
  "referenceId": "loan_id",
  "referenceType": "loan_disbursement"
}
```

### GET /api/transactions/:id
Get transaction details

### PUT /api/transactions/:id
Update transaction

### DELETE /api/transactions/:id
Delete transaction

### GET /api/transactions/summary
Get financial summary

**Query Parameters:**
- `branchId` (optional): Filter by branch
- `startDate` (optional): Start date filter
- `endDate` (optional): End date filter

### GET /api/analytics/branch/:branchId
Get branch analytics (admin/manager)

### GET /api/analytics/system
Get system-wide analytics (admin only)

### POST /api/reports/generate
Generate custom reports

**Request Body:**
```json
{
  "reportType": "monthly_summary",
  "parameters": {
    "branchId": "branch_id",
    "year": 2024,
    "month": 1
  }
}
```

**Available Report Types:**
- `monthly_summary`: Monthly financial summary
- `branch_performance`: Branch performance comparison
- `loan_portfolio`: Loan portfolio analysis
- `collection_report`: Collection performance report

## Security Best Practices

1. **HTTPS Only**: Always use HTTPS in production
2. **Token Storage**: Store tokens securely (HTTP-only cookies recommended)
3. **Token Expiration**: Handle token expiration gracefully
4. **Rate Limiting**: Respect rate limits and implement backoff
5. **Input Validation**: Validate all inputs on client side
6. **Error Handling**: Don't expose sensitive information in errors
